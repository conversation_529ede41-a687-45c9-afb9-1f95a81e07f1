<template>
  <div class="reports-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>报表统计</h2>
        <p>查看销售数据和统计分析</p>
      </div>
      <div class="header-right">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleDateChange"
        />
        <el-button type="primary" @click="exportReport">
          <el-icon><Download /></el-icon>
          导出报表
        </el-button>
      </div>
    </div>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-overview">
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon orders">
              <el-icon size="24"><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ reportData.totalOrders }}</div>
              <div class="stat-label">总订单数</div>
              <div class="stat-change positive">+12.5%</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon revenue">
              <el-icon size="24"><Money /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">¥{{ reportData.totalRevenue }}</div>
              <div class="stat-label">总营业额</div>
              <div class="stat-change positive">+8.3%</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon average">
              <el-icon size="24"><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">¥{{ reportData.averageOrder }}</div>
              <div class="stat-label">平均客单价</div>
              <div class="stat-change negative">-2.1%</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon dishes">
              <el-icon size="24"><Food /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ reportData.totalDishes }}</div>
              <div class="stat-label">售出菜品数</div>
              <div class="stat-change positive">+15.7%</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 销售趋势图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>销售趋势</span>
              <el-radio-group v-model="salesPeriod" size="small">
                <el-radio-button label="7d">近7天</el-radio-button>
                <el-radio-button label="30d">近30天</el-radio-button>
                <el-radio-button label="90d">近90天</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container">
            <v-chart :option="salesTrendOption" style="height: 350px;" />
          </div>
        </el-card>
      </el-col>

      <!-- 菜品销量排行 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <span>热销菜品TOP10</span>
          </template>
          <div class="chart-container">
            <v-chart :option="dishRankingOption" style="height: 350px;" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-row">
      <!-- 分类销售占比 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <span>菜品分类销售占比</span>
          </template>
          <div class="chart-container">
            <v-chart :option="categoryPieOption" style="height: 350px;" />
          </div>
        </el-card>
      </el-col>

      <!-- 时段分析 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <span>营业时段分析</span>
          </template>
          <div class="chart-container">
            <v-chart :option="hourlyAnalysisOption" style="height: 350px;" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>菜品销售明细</span>
          <el-input
            v-model="searchKeyword"
            placeholder="搜索菜品"
            style="width: 200px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </template>

      <el-table :data="filteredDishData" style="width: 100%">
        <el-table-column prop="rank" label="排名" width="80" />
        <el-table-column prop="dishName" label="菜品名称" />
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag :type="getCategoryType(row.category)" size="small">
              {{ getCategoryText(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="单价" width="100">
          <template #default="{ row }">
            ¥{{ row.price }}
          </template>
        </el-table-column>
        <el-table-column prop="soldCount" label="销量" width="100" sortable />
        <el-table-column prop="revenue" label="营业额" width="120" sortable>
          <template #default="{ row }">
            ¥{{ row.revenue }}
          </template>
        </el-table-column>
        <el-table-column prop="percentage" label="占比" width="100">
          <template #default="{ row }">
            {{ row.percentage }}%
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { exportOrderReport, exportSalesReport, exportDishReport, exportUserReport, downloadFile } from '@/api/export'
import {
  Download,
  Document,
  Money,
  TrendCharts,
  Food,
  Search
} from '@element-plus/icons-vue'

use([
  CanvasRenderer,
  LineChart,
  BarChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const dateRange = ref(['2023-07-01', '2023-07-31'])
const salesPeriod = ref('7d')
const searchKeyword = ref('')

const reportData = reactive({
  totalOrders: 1248,
  totalRevenue: 45680,
  averageOrder: 86.5,
  totalDishes: 3256
})

const dishData = ref([
  { rank: 1, dishName: '宫保鸡丁', category: 'MAIN', price: 38, soldCount: 156, revenue: 5928, percentage: 12.8 },
  { rank: 2, dishName: '清蒸鲈鱼', category: 'MAIN', price: 68, soldCount: 89, revenue: 6052, percentage: 13.2 },
  { rank: 3, dishName: '水果沙拉', category: 'DESSERT', price: 28, soldCount: 134, revenue: 3752, percentage: 8.2 },
  { rank: 4, dishName: '可乐', category: 'DRINK', price: 10, soldCount: 298, revenue: 2980, percentage: 6.5 },
  { rank: 5, dishName: '凉拌黄瓜', category: 'APPETIZER', price: 18, soldCount: 167, revenue: 3006, percentage: 6.6 }
])

const filteredDishData = computed(() => {
  if (!searchKeyword.value) return dishData.value
  return dishData.value.filter(dish =>
    dish.dishName.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const salesTrendOption = ref({
  tooltip: { trigger: 'axis' },
  legend: { data: ['订单数', '营业额'] },
  xAxis: {
    type: 'category',
    data: ['7-25', '7-26', '7-27', '7-28', '7-29', '7-30', '7-31']
  },
  yAxis: [
    { type: 'value', name: '订单数' },
    { type: 'value', name: '营业额(元)' }
  ],
  series: [
    {
      name: '订单数',
      type: 'line',
      data: [45, 52, 38, 67, 73, 89, 94],
      itemStyle: { color: '#409eff' }
    },
    {
      name: '营业额',
      type: 'line',
      yAxisIndex: 1,
      data: [1850, 2240, 1680, 2890, 3150, 3840, 4050],
      itemStyle: { color: '#67c23a' }
    }
  ]
})

const dishRankingOption = ref({
  tooltip: { trigger: 'axis' },
  xAxis: { type: 'value' },
  yAxis: {
    type: 'category',
    data: ['凉拌黄瓜', '可乐', '水果沙拉', '清蒸鲈鱼', '宫保鸡丁']
  },
  series: [{
    type: 'bar',
    data: [167, 298, 134, 89, 156],
    itemStyle: { color: '#e6a23c' }
  }]
})

const categoryPieOption = ref({
  tooltip: { trigger: 'item' },
  legend: { orient: 'vertical', left: 'left' },
  series: [{
    type: 'pie',
    radius: '60%',
    data: [
      { value: 35, name: '主菜' },
      { value: 25, name: '饮品' },
      { value: 20, name: '甜点' },
      { value: 20, name: '开胃菜' }
    ]
  }]
})

const hourlyAnalysisOption = ref({
  tooltip: { trigger: 'axis' },
  xAxis: {
    type: 'category',
    data: ['9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00']
  },
  yAxis: { type: 'value' },
  series: [{
    type: 'bar',
    data: [5, 8, 15, 45, 38, 25, 18, 22, 35, 58, 72, 65, 42],
    itemStyle: { color: '#f56c6c' }
  }]
})

const getCategoryType = (category) => {
  const typeMap = {
    'APPETIZER': 'danger',
    'MAIN': 'success',
    'DESSERT': 'warning',
    'DRINK': 'info'
  }
  return typeMap[category] || 'info'
}

const getCategoryText = (category) => {
  const textMap = {
    'APPETIZER': '开胃菜',
    'MAIN': '主菜',
    'DESSERT': '甜点',
    'DRINK': '饮品'
  }
  return textMap[category] || category
}

const handleDateChange = (dates) => {
  console.log('Date range changed:', dates)
  // 重新加载数据
}

const exportReport = async () => {
  try {
    // 显示导出选项对话框
    const { value: exportType } = await ElMessageBox.prompt('请选择导出类型', '导出报表', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputType: 'select',
      inputOptions: {
        'orders': '订单报表',
        'sales': '销售报表',
        'dishes': '菜品报表',
        'users': '用户报表'
      },
      inputValue: 'orders'
    })

    if (!exportType) return

    // 显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: '正在导出报表...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      let response
      const params = {
        startDate: dateRange.value?.[0],
        endDate: dateRange.value?.[1]
      }

      // 根据选择的类型调用不同的导出API
      switch (exportType) {
        case 'orders':
          response = await exportOrderReport(params)
          break
        case 'sales':
          response = await exportSalesReport(params)
          break
        case 'dishes':
          response = await exportDishReport(params)
          break
        case 'users':
          response = await exportUserReport(params)
          break
        default:
          throw new Error('未知的导出类型')
      }

      // 生成文件名
      const now = new Date()
      const dateStr = now.toISOString().split('T')[0]
      const typeMap = {
        'orders': '订单报表',
        'sales': '销售报表',
        'dishes': '菜品报表',
        'users': '用户报表'
      }
      const filename = `${typeMap[exportType]}_${dateStr}.xlsx`

      // 下载文件
      downloadFile(response.data, filename)
      ElMessage.success('报表导出成功')

    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败，请稍后重试')
    } finally {
      loading.close()
    }

  } catch (error) {
    // 用户取消了操作
  }
}

onMounted(() => {
  // 加载报表数据
  console.log('Reports page mounted')
})
</script>

<style scoped>
.reports-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.stats-overview {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.orders {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.revenue {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.average {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.dishes {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin: 4px 0;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #67c23a;
}

.stat-change.negative {
  color: #f56c6c;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-card,
.table-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  padding: 10px 0;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-right {
    width: 100%;
    flex-direction: column;
  }
  
  .charts-row .el-col {
    margin-bottom: 20px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}
</style>
